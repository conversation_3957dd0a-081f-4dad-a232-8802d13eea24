# 编译配置命令

**配置编译目标为 ESP32S3：**

```bash
idf.py set-target esp32s3
```

**打开 menuconfig：**

```bash
idf.py menuconfig
```

**选择板子：**

```
<PERSON>zhi Assistant -> Board Type -> Movecall Moji 小智AI衍生版
```

**编译：**

```bash
idf.py build
```

# 按钮操作说明书

## 基本按钮功能

### Boot按钮
- **单击**: 切换聊天状态（开始/停止聊天）
- **双击**: 关闭所有设备（紧急停止所有功能）
- **长按**: 在启动时重置WiFi配置

### 音量按钮
- **音量+按钮单击**: 增加音量（每次+10%）
- **音量-按钮单击**: 降低音量（每次-10%）

## 夹吸功能（气泵控制）

### 气泵按钮
- **单击**: 
  - 当关闭状态时：开启夹吸功能，设置为模式0
  - 当开启状态时：切换到下一个夹吸模式（共12种模式）
- **双击**: 调整夹吸力度（20%-100%，每次+10%，超过100%后重置为20%）
- **长按**: 
  - 当开启状态时：关闭夹吸功能
  - 当关闭状态时：调整夹吸力度（同双击效果）

### 夹吸模式说明
共12种频率模式，从慢到快：
- 模式0: 1.0秒/次
- 模式1: 0.8秒/次
- 模式2: 0.6秒/次
- 模式3: 0.5秒/次
- 模式4: 0.4秒/次
- 模式5: 0.3秒/次
- 模式6: 0.25秒/次
- 模式7: 0.2秒/次
- 模式8: 0.15秒/次
- 模式9: 0.1秒/次
- 模式10: 0.08秒/次
- 模式11: 0.05秒/次

## 跳蛋功能（震动控制）

### 跳蛋按钮
- **单击**: 
  - 当关闭状态时：开启跳蛋功能，设置为模式0
  - 当开启状态时：切换到下一个震动模式（共8种模式）
- **双击**: 调整震动力度（20%-100%，每次+10%，超过100%后重置为20%）
- **长按**: 
  - 当开启状态时：关闭跳蛋功能
  - 当关闭状态时：调整震动力度（同双击效果）

### 震动模式说明
共8种频率模式，从慢到快：
- 模式0: 1.0秒/次
- 模式1: 0.8秒/次
- 模式2: 0.6秒/次
- 模式3: 0.4秒/次
- 模式4: 0.3秒/次
- 模式5: 0.2秒/次
- 模式6: 0.1秒/次
- 模式7: 0.05秒/次

## 力度说明
- 力度范围：20%-100%
- 默认力度：80%
- 力度越高，设备震动/夹吸频率越快
- 调整力度后，如果设备正在运行，会立即应用新的力度设置

## 紧急停止
如需紧急停止所有设备，双击Boot按钮即可立即关闭所有功能。