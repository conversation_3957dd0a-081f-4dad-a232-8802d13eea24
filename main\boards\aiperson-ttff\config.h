#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

// Movecall Moji configuration

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_6
#define AUDIO_I2S_GPIO_WS GPIO_NUM_12
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_14
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_13
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_11

#define AUDIO_CODEC_PA_PIN       GPIO_NUM_9
#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_5
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_4
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR

#define BUILTIN_LED_GPIO        GPIO_NUM_21
#define BOOT_BUTTON_GPIO        GPIO_NUM_0

#define JYCB_PIN_BAT  GPIO_NUM_8  // 电量检测引脚
#define JYCB_PIN_QB  GPIO_NUM_38  // 气泵引脚(飞机杯)
#define JYCB_PIN_TDL  GPIO_NUM_41  // 电臀脚(臀部组件)
#define JYCB_PIN_TDM  GPIO_NUM_42  // 跳蛋引脚(跳蛋组件)
#define JYCB_PIN_CKM  GPIO_NUM_10  // 震动检测引脚
#define JYCB_PIN_TOUCH  GPIO_NUM_45  // 触摸引脚

// 按钮定义
#define BUTTON_VOLUME_UP_GPIO   GPIO_NUM_40  // 音量加按钮
#define BUTTON_VOLUME_DOWN_GPIO GPIO_NUM_39  // 音量减按钮
#define BUTTON_PUMP_GPIO        GPIO_NUM_15  // 气泵按钮
#define BUTTON_TDL_GPIO         GPIO_NUM_7  // 跳蛋按钮 (跳蛋+电臀)


#endif // _BOARD_CONFIG_H_
