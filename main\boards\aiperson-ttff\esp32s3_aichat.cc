#include "wifi_board.h"
#include "audio_codecs/es8311_audio_codec.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"

#include <wifi_station.h>
#include <esp_log.h>
#include <esp_efuse_table.h>
#include <esp_adc/adc_oneshot.h>
#include <esp_adc/adc_cali.h>
#include <driver/i2c_master.h>

#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <esp_lcd_gc9a01.h>

#include "driver/gpio.h"
#include "driver/spi_master.h"

#include <esp_timer.h>

#define TAG "ESP32S3_JYCB"

// ADC配置
#define BAT_ADC_CHANNEL ADC_CHANNEL_8 // 对应GPIO_NUM_8
#define BAT_ADC_UNIT ADC_UNIT_1
#define BAT_ADC_ATTEN ADC_ATTEN_DB_12 // 0-3.3V (替换已弃用的ADC_ATTEN_DB_11)
#define BAT_FULL_VOLTAGE 4200 // 满电压4.2V
#define BAT_MIN_VOLTAGE 3300 // 最低电压3.3V

// 控制引脚定义
#define AIR_PUMP_GPIO      JYCB_PIN_QB    // 气泵控制引脚
#define LARGE_VIBRATOR_GPIO JYCB_PIN_TDM  // 大跳蛋控制引脚
#define SMALL_VIBRATOR_GPIO JYCB_PIN_TDL  // 小跳蛋控制引脚

// 夹吸模式数量
#define PUMP_MODE_COUNT 12
// 跳蛋模式数量
#define VIBRATOR_MODE_COUNT 8

// 力度设置
#define MIN_INTENSITY 20  // 最小力度
#define MAX_INTENSITY 100 // 最大力度
#define INTENSITY_STEP 10 // 力度调整步长

class ESP32S3_JYCB : public WifiBoard {
private:
    i2c_master_bus_handle_t codec_i2c_bus_ = NULL;
    Button boot_button_;
    Button volume_up_button_;
    Button volume_down_button_;
    Button pump_button_;
    Button tdl_button_;
    adc_oneshot_unit_handle_t adc_handle_ = NULL;
    adc_cali_handle_t adc_cali_handle_ = NULL;
    bool adc_calibrated_ = false;
    
    // 夹吸和跳蛋状态变量
    bool pump_on_ = false;
    bool vibrators_on_ = false;
    int pump_mode_ = 0;
    int vibrator_mode_ = 0;
    int pump_intensity_ = 80;      // 夹吸力度，默认80%
    int vibrator_intensity_ = 80;  // 跳蛋力度，默认80%
    esp_timer_handle_t pump_timer_ = nullptr;
    esp_timer_handle_t vibrator_timer_ = nullptr;
    
    void InitializeCodecI2c() {
        ESP_LOGI(TAG, "Initializing I2C bus for audio codec");
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = I2C_NUM_0,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .glitch_ignore_cnt = 7,
            .intr_priority = 0,
            .trans_queue_depth = 0,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        
        esp_err_t ret = i2c_new_master_bus(&i2c_bus_cfg, &codec_i2c_bus_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize I2C bus: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "I2C bus initialized successfully");
        }
    }

    void InitializeButtons() {
        ESP_LOGI(TAG, "Initializing buttons");
        
        // 初始化Boot按钮
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ESP_LOGI(TAG, "Boot button pressed during startup, resetting WiFi config");
                ResetWifiConfiguration();
            }
            ESP_LOGI(TAG, "Toggling chat state");
            app.ToggleChatState();
        });
        
        // 初始化音量加按钮
        volume_up_button_.OnClick([this]() {
            ESP_LOGI(TAG, "Volume up button pressed");
            auto codec = GetAudioCodec();
            auto volume = codec->output_volume() + 10;
            if (volume > 100) {
                volume = 100;
            }
            codec->SetOutputVolume(volume);
            ESP_LOGI(TAG, "Volume increased to %d", volume);
        });
        
        // 初始化音量减按钮
        volume_down_button_.OnClick([this]() {
            ESP_LOGI(TAG, "Volume down button pressed");
            auto codec = GetAudioCodec();
            auto volume = codec->output_volume() - 10;
            if (volume < 0) {
                volume = 0;
            }
            codec->SetOutputVolume(volume);
            ESP_LOGI(TAG, "Volume decreased to %d", volume);
        });
        
        // 初始化气泵按钮(夹吸功能)
        pump_button_.OnClick([this]() {
            ESP_LOGI(TAG, "Pump button pressed");
            if (!pump_on_) {
                // 如果当前是关闭状态，则开启并设置为模式0
                pump_on_ = true;
                pump_mode_ = 0;
                StartPumpMode(pump_mode_);
                ESP_LOGI(TAG, "Air pump turned ON, mode: %d, intensity: %d%%", pump_mode_, pump_intensity_);
            } else {
                // 如果已经开启，则切换到下一个模式
                pump_mode_ = (pump_mode_ + 1) % PUMP_MODE_COUNT;
                StartPumpMode(pump_mode_);
                ESP_LOGI(TAG, "Air pump mode changed to: %d, intensity: %d%%", pump_mode_, pump_intensity_);
            }
        });
        
        // 长按气泵按钮关闭夹吸功能
        pump_button_.OnLongPress([this]() {
            if (pump_on_) {
                StopPump();
                pump_on_ = false;
                ESP_LOGI(TAG, "Air pump turned OFF by long press");
            } else {
                // 如果已经关闭，长按则增加力度
                pump_intensity_ += INTENSITY_STEP;
                if (pump_intensity_ > MAX_INTENSITY) {
                    pump_intensity_ = MIN_INTENSITY;
                }
                ESP_LOGI(TAG, "Air pump intensity set to: %d%%", pump_intensity_);
            }
        });
        
        // 双击气泵按钮调整力度
        pump_button_.OnDoubleClick([this]() {
            pump_intensity_ += INTENSITY_STEP;
            if (pump_intensity_ > MAX_INTENSITY) {
                pump_intensity_ = MIN_INTENSITY;
            }
            if (pump_on_) {
                StartPumpMode(pump_mode_); // 重新启动以应用新力度
            }
            ESP_LOGI(TAG, "Air pump intensity set to: %d%%", pump_intensity_);
        });
        
        // 初始化跳蛋按钮 - 控制小跳蛋和大跳蛋
        tdl_button_.OnClick([this]() {
            ESP_LOGI(TAG, "TDL button pressed");
            if (!vibrators_on_) {
                // 如果当前是关闭状态，则开启并设置为模式0
                vibrators_on_ = true;
                vibrator_mode_ = 0;
                StartVibratorMode(vibrator_mode_);
                ESP_LOGI(TAG, "Vibrators turned ON, mode: %d, intensity: %d%%", vibrator_mode_, vibrator_intensity_);
            } else {
                // 如果已经开启，则切换到下一个模式
                vibrator_mode_ = (vibrator_mode_ + 1) % VIBRATOR_MODE_COUNT;
                StartVibratorMode(vibrator_mode_);
                ESP_LOGI(TAG, "Vibrator mode changed to: %d, intensity: %d%%", vibrator_mode_, vibrator_intensity_);
            }
        });
        
        // 长按跳蛋按钮关闭功能
        tdl_button_.OnLongPress([this]() {
            if (vibrators_on_) {
                StopVibrators();
                vibrators_on_ = false;
                ESP_LOGI(TAG, "Vibrators turned OFF by long press");
            } else {
                // 如果已经关闭，长按则增加力度
                vibrator_intensity_ += INTENSITY_STEP;
                if (vibrator_intensity_ > MAX_INTENSITY) {
                    vibrator_intensity_ = MIN_INTENSITY;
                }
                ESP_LOGI(TAG, "Vibrator intensity set to: %d%%", vibrator_intensity_);
            }
        });
        
        // 双击跳蛋按钮调整力度
        tdl_button_.OnDoubleClick([this]() {
            vibrator_intensity_ += INTENSITY_STEP;
            if (vibrator_intensity_ > MAX_INTENSITY) {
                vibrator_intensity_ = MIN_INTENSITY;
            }
            if (vibrators_on_) {
                StartVibratorMode(vibrator_mode_); // 重新启动以应用新力度
            }
            ESP_LOGI(TAG, "Vibrator intensity set to: %d%%", vibrator_intensity_);
        });
        
        // 添加双击Boot按钮关闭所有设备的功能
        boot_button_.OnDoubleClick([this]() {
            ESP_LOGI(TAG, "Boot button double clicked, turning off all devices");
            // 关闭所有设备
            StopPump();
            StopVibrators();
            pump_on_ = false;
            vibrators_on_ = false;
            ESP_LOGI(TAG, "All devices turned off");
        });
    }
    
    // 开始指定的夹吸模式
    void StartPumpMode(int mode) {
        // 如果已有定时器在运行，先停止
        StopPump();
        
        // 根据不同模式设置不同的频率
        int interval_ms = 0;
        switch (mode) {
            case 0: interval_ms = 1000; break;  // 1秒
            case 1: interval_ms = 800; break;   // 0.8秒
            case 2: interval_ms = 600; break;   // 0.6秒
            case 3: interval_ms = 500; break;   // 0.5秒
            case 4: interval_ms = 400; break;   // 0.4秒
            case 5: interval_ms = 300; break;   // 0.3秒
            case 6: interval_ms = 250; break;   // 0.25秒
            case 7: interval_ms = 200; break;   // 0.2秒
            case 8: interval_ms = 150; break;   // 0.15秒
            case 9: interval_ms = 100; break;   // 0.1秒
            case 10: interval_ms = 80; break;   // 0.08秒
            case 11: interval_ms = 50; break;   // 0.05秒
            default: interval_ms = 500; break;  // 默认0.5秒
        }
        
        // 根据强度调整间隔时间
        // 强度越高，间隔越短，频率越快
        interval_ms = interval_ms * (100 - (pump_intensity_ - MIN_INTENSITY)) / (100 - MIN_INTENSITY);
        if (interval_ms < 30) interval_ms = 30; // 确保最小间隔
        
        // 创建定时器
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                // 移除未使用的变量警告
                // ESP32S3_JYCB* self = static_cast<ESP32S3_JYCB*>(arg);
                static bool state = false;
                state = !state;
                gpio_set_level(AIR_PUMP_GPIO, state ? 1 : 0);
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "pump_timer",
            .skip_unhandled_events = true,
        };
        
        if (pump_timer_ == nullptr) {
            ESP_ERROR_CHECK(esp_timer_create(&timer_args, &pump_timer_));
        }
        
        // 启动定时器
        ESP_ERROR_CHECK(esp_timer_start_periodic(pump_timer_, interval_ms * 1000)); // 转换为微秒
        ESP_LOGI(TAG, "Pump started with interval: %d ms, intensity: %d%%", interval_ms, pump_intensity_);
    }
    
    // 停止夹吸功能
    void StopPump() {
        if (pump_timer_ != nullptr) {
            esp_timer_stop(pump_timer_);
        }
        gpio_set_level(AIR_PUMP_GPIO, 0);
    }
    
    // 开始指定的跳蛋模式
    void StartVibratorMode(int mode) {
        // 如果已有定时器在运行，先停止
        StopVibrators();
        
        // 根据不同模式设置不同的频率
        int interval_ms = 0;
        switch (mode) {
            case 0: interval_ms = 1000; break;  // 1秒
            case 1: interval_ms = 800; break;   // 0.8秒
            case 2: interval_ms = 600; break;   // 0.6秒
            case 3: interval_ms = 400; break;   // 0.4秒
            case 4: interval_ms = 300; break;   // 0.3秒
            case 5: interval_ms = 200; break;   // 0.2秒
            case 6: interval_ms = 100; break;   // 0.1秒
            case 7: interval_ms = 50; break;    // 0.05秒
            default: interval_ms = 500; break;  // 默认0.5秒
        }
        
        // 根据强度调整间隔时间
        // 强度越高，间隔越短，频率越快
        interval_ms = interval_ms * (100 - (vibrator_intensity_ - MIN_INTENSITY)) / (100 - MIN_INTENSITY);
        if (interval_ms < 30) interval_ms = 30; // 确保最小间隔
        
        // 创建定时器
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                // 移除未使用的变量警告
                // ESP32S3_JYCB* self = static_cast<ESP32S3_JYCB*>(arg);
                static bool state = false;
                state = !state;
                gpio_set_level(SMALL_VIBRATOR_GPIO, state ? 1 : 0);
                gpio_set_level(LARGE_VIBRATOR_GPIO, state ? 1 : 0);
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "vibrator_timer",
            .skip_unhandled_events = true,
        };
        
        if (vibrator_timer_ == nullptr) {
            ESP_ERROR_CHECK(esp_timer_create(&timer_args, &vibrator_timer_));
        }
        
        // 启动定时器
        ESP_ERROR_CHECK(esp_timer_start_periodic(vibrator_timer_, interval_ms * 1000)); // 转换为微秒
        ESP_LOGI(TAG, "Vibrators started with interval: %d ms, intensity: %d%%", interval_ms, vibrator_intensity_);
    }
    
    // 停止跳蛋功能
    void StopVibrators() {
        if (vibrator_timer_ != nullptr) {
            esp_timer_stop(vibrator_timer_);
        }
        gpio_set_level(SMALL_VIBRATOR_GPIO, 0);
        gpio_set_level(LARGE_VIBRATOR_GPIO, 0);
    }
    
    void InitializeADC() {
        ESP_LOGI(TAG, "Initializing ADC for battery monitoring");
        // 初始化ADC
        adc_oneshot_unit_init_cfg_t init_config = {
            .unit_id = BAT_ADC_UNIT,
            .ulp_mode = ADC_ULP_MODE_DISABLE,
        };
        ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config, &adc_handle_));
        
        // 配置ADC通道
        adc_oneshot_chan_cfg_t config = {
            .atten = BAT_ADC_ATTEN,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ESP_ERROR_CHECK(adc_oneshot_config_channel(adc_handle_, BAT_ADC_CHANNEL, &config));
        
        // 尝试校准ADC - 使用curve_fitting替代line_fitting
        adc_cali_curve_fitting_config_t cali_config = {
            .unit_id = BAT_ADC_UNIT,
            .atten = BAT_ADC_ATTEN,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        esp_err_t ret = adc_cali_create_scheme_curve_fitting(&cali_config, &adc_cali_handle_);
        if (ret == ESP_OK) {
            adc_calibrated_ = true;
            ESP_LOGI(TAG, "ADC calibration enabled");
        } else {
            adc_calibrated_ = false;
            ESP_LOGW(TAG, "ADC calibration failed: %s", esp_err_to_name(ret));
        }
    }

    // 初始化控制IO引脚，确保所有设备初始状态为关闭
    void InitializeControlGPIOs() {
        ESP_LOGI(TAG, "Initializing control GPIOs");
        
        // 配置气泵控制引脚
        gpio_config_t air_pump_conf = {
            .pin_bit_mask = (1ULL << AIR_PUMP_GPIO),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_ENABLE,  // 使能下拉电阻
            .intr_type = GPIO_INTR_DISABLE
        };
        gpio_config(&air_pump_conf);
        gpio_set_level(AIR_PUMP_GPIO, 0);  // 确保初始状态为低电平
        ESP_LOGI(TAG, "Air pump GPIO initialized and set LOW");
        
        // 配置大跳蛋控制引脚
        gpio_config_t large_vib_conf = {
            .pin_bit_mask = (1ULL << LARGE_VIBRATOR_GPIO),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_ENABLE,  // 使能下拉电阻
            .intr_type = GPIO_INTR_DISABLE
        };
        gpio_config(&large_vib_conf);
        gpio_set_level(LARGE_VIBRATOR_GPIO, 0);  // 确保初始状态为低电平
        ESP_LOGI(TAG, "Large vibrator GPIO initialized and set LOW");
        
        // 配置小跳蛋控制引脚
        gpio_config_t small_vib_conf = {
            .pin_bit_mask = (1ULL << SMALL_VIBRATOR_GPIO),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_ENABLE,  // 使能下拉电阻
            .intr_type = GPIO_INTR_DISABLE
        };
        gpio_config(&small_vib_conf);
        gpio_set_level(SMALL_VIBRATOR_GPIO, 0);  // 确保初始状态为低电平
        ESP_LOGI(TAG, "Small vibrator GPIO initialized and set LOW");
    }

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
        ESP_LOGI(TAG, "Initializing IoT devices");
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker")); //扬声器
        thing_manager.AddThing(iot::CreateThing("Battery")); //电池电量
        
        // 添加艾珀森板子特有的物联网设备
        ESP_LOGI(TAG, "Adding TTFF specific IoT devices");
        thing_manager.AddThing(iot::CreateThing("SmallVibrator")); // 小跳蛋
        thing_manager.AddThing(iot::CreateThing("LargeVibrator")); // 大跳蛋
        thing_manager.AddThing(iot::CreateThing("AirPump"));  // 气泵
    }

public:
    ESP32S3_JYCB() : 
        boot_button_(BOOT_BUTTON_GPIO),
        volume_up_button_(BUTTON_VOLUME_UP_GPIO),
        volume_down_button_(BUTTON_VOLUME_DOWN_GPIO),
        pump_button_(BUTTON_PUMP_GPIO),
        tdl_button_(BUTTON_TDL_GPIO) {
        ESP_LOGI(TAG, "Initializing ESP32S3_JYCB board");
        
        // 首先初始化控制IO引脚，确保设备处于安全状态
        InitializeControlGPIOs();
        
        InitializeCodecI2c();
        InitializeButtons();
        InitializeADC();
        InitializeIot();
        ESP_LOGI(TAG, "Board initialization completed");
    }
    
    ~ESP32S3_JYCB() {
        // 释放资源前确保所有控制引脚处于低电平
        gpio_set_level(AIR_PUMP_GPIO, 0);
        gpio_set_level(LARGE_VIBRATOR_GPIO, 0);
        gpio_set_level(SMALL_VIBRATOR_GPIO, 0);
        
        // 释放资源
        if (adc_cali_handle_) {
            adc_cali_delete_scheme_curve_fitting(adc_cali_handle_);
        }
        if (adc_handle_) {
            adc_oneshot_del_unit(adc_handle_);
        }
        
        // I2C总线在程序退出时由ESP-IDF自动释放
    }

    virtual Led* GetLed() override {
        static SingleLed led_strip(BUILTIN_LED_GPIO);
        return &led_strip;
    }

    virtual AudioCodec* GetAudioCodec() override {
        static Es8311AudioCodec audio_codec(codec_i2c_bus_, I2C_NUM_0, AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_MCLK, AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN,
            AUDIO_CODEC_PA_PIN, AUDIO_CODEC_ES8311_ADDR);
        return &audio_codec;
    }
    
    // 实现电池电量检测
    virtual bool GetBatteryLevel(int& level, bool& charging, bool& discharging) override {
        int voltage_mv = 0;
        
        if (!adc_handle_) {
            ESP_LOGE(TAG, "ADC not initialized");
            return false;
        }
        
        // 读取原始ADC值
        int adc_raw;
        esp_err_t ret = adc_oneshot_read(adc_handle_, BAT_ADC_CHANNEL, &adc_raw);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
            return false;
        }
        
        // 转换为电压值
        if (adc_calibrated_) {
            ret = adc_cali_raw_to_voltage(adc_cali_handle_, adc_raw, &voltage_mv);
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to convert ADC to voltage: %s", esp_err_to_name(ret));
                return false;
            }
        } else {
            // 如果没有校准，使用简单的线性映射
            voltage_mv = (adc_raw * 3300) / 4095; // 假设ADC最大值为4095，参考电压为3.3V
        }
        
        // 电压值需要根据分压电路转换为实际电池电压
        // 假设使用的是一个简单的分压器，实际值可能需要调整
        voltage_mv = voltage_mv * 2; // 假设1:1分压
        
        // 将电压转换为百分比
        if (voltage_mv >= BAT_FULL_VOLTAGE) {
            level = 100;
        } else if (voltage_mv <= BAT_MIN_VOLTAGE) {
            level = 0;
        } else {
            level = ((voltage_mv - BAT_MIN_VOLTAGE) * 100) / (BAT_FULL_VOLTAGE - BAT_MIN_VOLTAGE);
        }
        
        // 确保范围在0-100之间
        if (level > 100) level = 100;
        if (level < 0) level = 0;
        
        // TODO: 根据电路实际情况检测充电和放电状态
        // 这里简单地假设USB连接时为充电状态
        charging = false;   // 默认为不充电
        discharging = true; // 默认为放电状态
        
        ESP_LOGD(TAG, "Battery: ADC=%d, Voltage=%dmV, Level=%d%%", adc_raw, voltage_mv, level);
        return true;
    }
};

DECLARE_BOARD(ESP32S3_JYCB)